# 📋 PLAN DE RESTRUCTURATION - Projet Edara

## 🎯 OBJECTIFS
1. **Réorganiser** la structure des fichiers selon les bonnes pratiques
2. **Supprimer** tous les fichiers inutiles et temporaires
3. **Optimiser** la clarté du code et la maintenabilité
4. **Standardiser** l'organisation pour faciliter les évolutions futures

## 📁 STRUCTURE ACTUELLE (PROBLÈMES IDENTIFIÉS)

### ❌ Fichiers en désordre à la racine :
- `main.js`, `preload.js`, `splash.html` (mélangés)
- Nombreux fichiers de documentation temporaires (*.md)
- Fichiers de test dispersés (`test-*.js`, `test-*.html`)
- Fichiers obsolètes (`index-final.html`, `splash-final.html`, etc.)

### ❌ Structure src/ incomplète :
- Modules dans `src/main/` mais pas d'organisation claire
- Pas de séparation claire entre main/renderer/shared

## 🏗️ NOUVELLE STRUCTURE PROPOSÉE

```
edara-electron-app/
├── 📁 src/
│   ├── 📁 main/                    # Processus principal Electron
│   │   ├── main.js                 # Point d'entrée principal
│   │   ├── preload.js              # Script de préchargement
│   │   ├── 📁 modules/             # Modules métier
│   │   │   ├── server-discovery.js
│   │   │   ├── odoo-auth.js
│   │   │   ├── connection-manager.js
│   │   │   └── window-manager.js
│   │   └── 📁 config/              # Configuration
│   │       └── servers.txt         # Configuration serveurs
│   ├── 📁 renderer/                # Interface utilisateur
│   │   ├── 📁 views/               # Vues/Pages
│   │   │   └── splash.html         # Page splash
│   │   ├── 📁 assets/              # Ressources statiques
│   │   │   ├── 📁 images/          # Images et logos
│   │   │   │   ├── logo-light.png
│   │   │   │   ├── logo-dark.png
│   │   │   │   └── illustration.svg
│   │   │   └── 📁 styles/          # Feuilles de style
│   │   │       └── splash.css
│   │   └── 📁 scripts/             # Scripts renderer
│   │       └── login-handler.js
│   └── 📁 shared/                  # Code partagé
│       └── 📁 constants/           # Constantes
│           └── app-constants.js
├── 📁 tests/                       # Tests organisés
│   ├── 📁 unit/                    # Tests unitaires
│   │   ├── server-discovery.test.js
│   │   └── odoo-auth.test.js
│   └── 📁 integration/             # Tests d'intégration
│       └── app-flow.test.js
├── 📁 docs/                        # Documentation
│   ├── README.md                   # Documentation principale
│   ├── INSTALLATION.md             # Guide d'installation
│   ├── CONFIGURATION.md            # Guide de configuration
│   └── DEVELOPMENT.md              # Guide de développement
├── 📁 build/                       # Configuration de build
│   └── electron-builder.json      # Config Electron Builder
├── package.json                    # Dépendances et scripts
├── package-lock.json              # Lock des dépendances
└── .gitignore                     # Fichiers à ignorer
```

## 🗑️ FICHIERS À SUPPRIMER

### Documentation temporaire (*.md) :
- `CORRECTIONS_ODOO.md`
- `CORRECTION_LOGOS_SPLASH.md`
- `CORRECTION_TRANSITION.md`
- `FENETRE_UNIQUE.md`
- `GESTION_DECONNEXION_ODOO.md`
- `GUIDE_LOGOS_SPLASH.md`
- `GUIDE_TAILLE_LOGO.md`
- `MISSION_ACCOMPLIE.md`
- `MISSION_FINALE_ACCOMPLIE.md`
- `NETTOYAGE_EFFECTUE.md`
- `PROBLEMES_RESOLUS.md`
- `SOLUTION_COMPLETE_FINALE.md`
- `SOLUTION_ODOO13_DIRECTE.md`
- `SPLASH_SCREEN_INTELLIGENT.md`
- `SUPPRESSION_LOGO_ESTOMPE.md`

### Fichiers de test temporaires :
- `test-logos.html`
- `test-odoo13-auth.js`
- `test-server-discovery.js`

### Fichiers obsolètes :
- `index-final.html`
- `splash-final.html`
- `style-final.css`
- `start-optimized.js`

## 🔄 MIGRATIONS À EFFECTUER

### 1. **Déplacements de fichiers :**
- `main.js` → `src/main/main.js`
- `preload.js` → `src/main/preload.js`
- `splash.html` → `src/renderer/views/splash.html`
- `serveur_ip.txt` → `src/main/config/servers.txt`
- `img/` → `src/renderer/assets/images/`

### 2. **Réorganisation des modules :**
- Garder `src/main/modules/` tel quel
- Déplacer `src/renderer/login-handler.js` → `src/renderer/scripts/`

### 3. **Mise à jour des imports :**
- Corriger tous les chemins d'import dans les fichiers
- Mettre à jour les références dans `package.json`

## 📝 ÉTAPES D'EXÉCUTION

1. **Créer la nouvelle structure** de dossiers
2. **Déplacer les fichiers** essentiels
3. **Supprimer les fichiers** inutiles
4. **Mettre à jour les imports** et références
5. **Tester le fonctionnement** après restructuration
6. **Créer la documentation** finale

## ✅ AVANTAGES ATTENDUS

1. **Structure claire** et professionnelle
2. **Séparation des responsabilités** (main/renderer/shared)
3. **Facilité de maintenance** et d'évolution
4. **Tests organisés** et maintenables
5. **Documentation centralisée**
6. **Conformité aux standards** Electron

## 🎯 RÉSULTAT FINAL

Un projet **propre**, **organisé** et **maintenable** qui respecte les bonnes pratiques de développement Electron et facilite les futures évolutions.
