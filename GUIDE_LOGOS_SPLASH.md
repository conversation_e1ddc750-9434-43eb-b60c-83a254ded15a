# 🎨 Guide d'Utilisation des Logos dans le Splash Screen

## 📁 Structure des Logos

Votre dossier `img/` contient maintenant :
```
img/
├── logo-edara-claire.png    # Logo clair (pour fond sombre)
├── logo-edara-noire.png     # Logo sombre (pour fond clair)
└── edara_illustration.svg   # Illustration vectorielle
```

## 🌓 Adaptation Automatique Mode Sombre/Clair

Le splash screen s'adapte automatiquement au mode système :

### Mode Sombre (par défaut)
- **Fond** : Sombre
- **Logo affiché** : `logo-edara-claire.png` (logo clair)
- **Logo estompé** : `logo-edara-claire.png` (en transparence)

### Mode Clair (détection automatique)
- **Fond** : Clair
- **Logo affiché** : `logo-edara-noire.png` (logo sombre)
- **Logo estompé** : `logo-edara-noire.png` (en transparence)

## 🔧 Configuration Actuelle

### Dans `splash.html` :

#### <PERSON><PERSON><PERSON> (Logo Principal)
```html
<div class="left-panel">
    <!-- Option 1: Illustration SVG (décommentez pour utiliser) -->
    <!-- <img src="img/edara_illustration.svg" alt="Illustration Edara" class="logo-adaptive"> -->
    
    <!-- Option 2: Logos adaptatifs selon le mode sombre/clair -->
    <img src="img/logo-edara-claire.png" alt="Logo Edara" class="logo-adaptive logo-light">
    <img src="img/logo-edara-noire.png" alt="Logo Edara" class="logo-adaptive logo-dark">
</div>
```

#### Logo Estompé (Coin Bas-Droite)
```html
<!-- Logos estompés adaptatifs -->
<img src="img/logo-edara-claire.png" alt="Faded Logo" class="faded-logo faded-logo-light">
<img src="img/logo-edara-noire.png" alt="Faded Logo" class="faded-logo faded-logo-dark">
```

## 🎛️ Options de Personnalisation

### Option 1 : Utiliser l'Illustration SVG
Pour utiliser l'illustration SVG au lieu des logos PNG :

1. **Commentez** les logos PNG :
```html
<!-- <img src="img/logo-edara-claire.png" alt="Logo Edara" class="logo-adaptive logo-light">
<img src="img/logo-edara-noire.png" alt="Logo Edara" class="logo-adaptive logo-dark"> -->
```

2. **Décommentez** l'illustration SVG :
```html
<img src="img/edara_illustration.svg" alt="Illustration Edara" class="logo-adaptive">
```

### Option 2 : Logos PNG Adaptatifs (Configuration Actuelle)
- Adaptation automatique selon le mode système
- Logo clair pour fond sombre
- Logo sombre pour fond clair

### Option 3 : Logo Unique
Pour utiliser un seul logo (sans adaptation) :
```html
<img src="img/logo-edara-claire.png" alt="Logo Edara" class="logo-adaptive">
```

## 🎨 CSS Responsable de l'Adaptation

```css
/* Par défaut (mode sombre) : logo clair */
.logo-light {
    display: block;
}

.logo-dark {
    display: none;
}

/* Mode clair : logo sombre */
@media (prefers-color-scheme: light) {
    .logo-light {
        display: none;
    }
    
    .logo-dark {
        display: block;
    }
}
```

## 📐 Dimensions et Styles

### Logo Principal (Panneau Gauche)
- **Taille** : 70% de la largeur du panneau
- **Position** : Centré
- **Effet** : Ombre portée avec `filter: drop-shadow()`

### Logo Estompé (Coin Bas-Droite)
- **Taille** : 120px × 110px
- **Position** : Absolue (bottom: 70px, right: 20px)
- **Opacité** : 0.17 (17%)
- **Effet** : Transition douce

## 🚀 Test des Modes

### Tester le Mode Sombre
```bash
# Sur macOS, activez le mode sombre dans Préférences Système
npm start
```

### Tester le Mode Clair
```bash
# Sur macOS, activez le mode clair dans Préférences Système
npm start
```

## 🔄 Changement de Logos

Pour changer les logos :

1. **Remplacez** les fichiers dans `img/` :
   - `logo-edara-claire.png` → Votre logo clair
   - `logo-edara-noire.png` → Votre logo sombre
   - `edara_illustration.svg` → Votre illustration

2. **Gardez les mêmes noms** pour que les références HTML restent valides

3. **Ou modifiez** les références dans `splash.html` :
```html
<img src="img/votre-nouveau-logo.png" alt="Logo" class="logo-adaptive">
```

## ✅ Validation

Le système fonctionne parfaitement avec :
- ✅ **Adaptation automatique** selon le mode système
- ✅ **Logos adaptatifs** (clair/sombre)
- ✅ **Transitions fluides** entre les modes
- ✅ **Logo estompé** adaptatif en bas à droite
- ✅ **Fallback** vers illustration SVG disponible

## 🎯 Résultat Final

Votre splash screen affiche maintenant :
1. **Logo principal** adaptatif selon le mode système
2. **Logo estompé** en bas à droite (même adaptation)
3. **Transitions fluides** lors du changement de mode
4. **Compatibilité** avec tous les formats (PNG, SVG)

**🎨 Votre branding Edara est maintenant parfaitement intégré !**
