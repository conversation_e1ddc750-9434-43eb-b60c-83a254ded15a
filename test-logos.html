<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logos Adaptatifs</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #2c3e50;
            color: white;
            transition: all 0.3s ease;
        }

        @media (prefers-color-scheme: light) {
            body {
                background: #f8f9fa;
                color: #333;
            }
        }

        .container {
            display: flex;
            height: 400px;
            border: 2px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
        }

        .left-panel {
            flex: 1;
            background: #34495e;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        @media (prefers-color-scheme: light) {
            .left-panel {
                background: #ffffff;
            }
        }

        .logo-adaptive {
            max-width: 70%;
            height: auto;
            transition: opacity 0.3s ease;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Mode sombre (par défaut) : logo clair */
        .logo-light {
            display: block;
        }

        .logo-dark {
            display: none;
        }

        /* Mode clair : logo sombre */
        @media (prefers-color-scheme: light) {
            .logo-light {
                display: none !important;
            }
            
            .logo-dark {
                display: block !important;
            }
        }

        .right-panel {
            flex: 2;
            background: #2c3e50;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        @media (prefers-color-scheme: light) {
            .right-panel {
                background: #f8f9fa;
            }
        }

        .info {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        @media (prefers-color-scheme: light) {
            .info {
                background: rgba(0, 0, 0, 0.05);
            }
        }

        .mode-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 10px;
            background: #e74c3c;
            color: white;
            border-radius: 4px;
            font-weight: bold;
        }

        @media (prefers-color-scheme: light) {
            .mode-indicator {
                background: #3498db;
                color: white;
            }
        }
    </style>
</head>
<body>
    <div class="mode-indicator" id="modeIndicator">Mode Sombre</div>
    
    <h1>Test des Logos Adaptatifs</h1>
    
    <div class="container">
        <div class="left-panel">
            <!-- Logos adaptatifs -->
            <img src="img/logo-edara-claire.png" alt="Logo Edara Clair" class="logo-adaptive logo-light">
            <img src="img/logo-edara-noire.png" alt="Logo Edara Sombre" class="logo-adaptive logo-dark">
        </div>
        
        <div class="right-panel">
            <div class="info">
                <strong>Mode Sombre (par défaut):</strong><br>
                Affiche logo-edara-claire.png (logo clair sur fond sombre)
            </div>
            
            <div class="info">
                <strong>Mode Clair:</strong><br>
                Affiche logo-edara-noire.png (logo sombre sur fond clair)
            </div>
            
            <div class="info">
                <strong>Test:</strong><br>
                Changez le mode sombre/clair dans les préférences système pour voir l'adaptation automatique.
            </div>
            
            <div class="info">
                <strong>Logos disponibles:</strong><br>
                • img/logo-edara-claire.png<br>
                • img/logo-edara-noire.png<br>
                • img/edara_illustration.svg
            </div>
        </div>
    </div>

    <script>
        // Détecter le mode et mettre à jour l'indicateur
        function updateModeIndicator() {
            const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const indicator = document.getElementById('modeIndicator');
            
            if (isDarkMode) {
                indicator.textContent = 'Mode Sombre';
                indicator.style.background = '#e74c3c';
            } else {
                indicator.textContent = 'Mode Clair';
                indicator.style.background = '#3498db';
            }
        }

        // Écouter les changements de mode
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateModeIndicator);
        
        // Initialiser
        updateModeIndicator();
    </script>
</body>
</html>
