# 🚀 Splash Screen Intelligent - Découverte Automatique des Serveurs

## 🎯 Objectif Atteint

**NOUVEAU COMPORTEMENT** : L'application démarre maintenant avec un splash screen intelligent qui :
1. **Vérifie automatiquement** la disponibilité des serveurs Odoo
2. **Priorise les connexions locales** si disponibles
3. **Fallback vers le serveur distant** si nécessaire
4. **Affiche un message d'erreur** si aucun serveur n'est accessible
5. **Permet de réessayer** en cas d'échec

## ✅ Séquence de Démarrage

### 1. Lancement de l'Application
```
npm start
  ↓
🚀 Création fenêtre splash
  ↓
📋 Lecture serveur_ip.txt
  ↓
🔍 Test serveurs en parallèle
  ↓
🎯 Sélection du meilleur serveur
  ↓
✅ Interface de connexion
```

### 2. Logique de Priorité
```
1. Serveurs locaux (192.168.x.x) - PRIORITÉ HAUTE
2. Serveur distant (https://edara.ligne-digitale.com) - FALLBACK
3. Aucun serveur → Message d'erreur + <PERSON><PERSON><PERSON> R<PERSON>sayer
```

## 🔧 Configuration via serveur_ip.txt

### Format du Fichier
```
# Commentaires ignorés
**************    # Serveur local 1
************      # Serveur local 2  
************      # Serveur local 3

# URL application distante
https://edara.ligne-digitale.com
```

### Traitement Automatique
- **Adresses IP** → Converties en `http://IP:8069`
- **URLs complètes** → Utilisées telles quelles
- **Commentaires** → Ignorés (lignes commençant par #)

## 📊 Interface Splash Screen

### États d'Affichage

#### 1. Initialisation
```
🔄 "Chargement de la configuration..."
Progress: 25%
```

#### 2. Test des Serveurs
```
🔍 "Test de X serveur(s)..."
Progress: 50%
Liste des serveurs avec statuts en temps réel
```

#### 3. Analyse des Résultats
```
📊 "Analyse des résultats..."
Progress: 75%
Affichage des temps de réponse
```

#### 4. Succès
```
✅ "Connexion [local/distant] établie"
Progress: 100%
Transition vers interface de connexion
```

#### 5. Échec
```
❌ "Service Edara non disponible"
Message d'erreur + Bouton "Réessayer"
```

## 🔧 Fichiers Implémentés

### Nouveau Module : `src/main/server-discovery.js`
- **Classe** : `ServerDiscovery`
- **Fonctions** :
  - `loadServerConfig()` - Lecture serveur_ip.txt
  - `testServerAvailability()` - Test connexion serveur
  - `discoverBestServer()` - Découverte intelligente
  - `getSelectedServer()` - Serveur sélectionné

### Splash Screen : `splash.html`
- **Interface** : Splash screen existant amélioré
- **JavaScript** : Écoute des événements de progression
- **APIs** : Communication avec Electron via IPC

### Gestionnaire Principal : `main.js`
- **Fonctions** :
  - `createSplashWindow()` - Création fenêtre splash
  - `startServerDiscovery()` - Lancement découverte
  - `createMainWindow()` - Création fenêtre principale
- **IPC Handlers** :
  - `discover-servers` - Découverte manuelle
  - `retry-server-discovery` - Nouvelle tentative

### Connection Manager : `src/main/connection-manager.js`
- **Nouvelles méthodes** :
  - `setPreferredServer()` - Définir serveur préféré
  - `getPreferredServer()` - Obtenir serveur préféré
  - `getBestServerWithPreference()` - Serveur avec préférence

### Preload : `preload.js`
- **Nouvelles APIs** :
  - `discoverServers()` - Lancer découverte
  - `retryServerCheck()` - Réessayer
  - `onSplashUpdate()` - Écouter mises à jour
  - `onSplashError()` - Écouter erreurs
  - `onSplashSuccess()` - Écouter succès

## 📋 Logs de Validation

### Démarrage Réussi (Serveur Local)
```
🚀 Création de la fenêtre splash...
✅ Fenêtre splash affichée
🔍 === DÉBUT DÉCOUVERTE AUTOMATIQUE DES SERVEURS ===
📋 Chargement de la configuration des serveurs...
✅ Configuration chargée: 3 serveurs locaux, 1 serveur distant
🔍 Test de 4 serveurs...
✅ Serveur local disponible: http://**************:8069 (45ms)
✅ Serveur local sélectionné: http://**************:8069 (45ms)
✅ Découverte réussie, transition vers l'interface de connexion
🎯 Configuration avec serveur sélectionné: http://**************:8069 (local)
```

### Fallback Serveur Distant
```
🔍 Test de 4 serveurs...
❌ Serveur local indisponible: http://**************:8069 - timeout
❌ Serveur local indisponible: http://************:8069 - timeout
❌ Serveur local indisponible: http://************:8069 - timeout
✅ Serveur distant disponible: https://edara.ligne-digitale.com (234ms)
✅ Serveur distant sélectionné: https://edara.ligne-digitale.com (234ms)
```

### Aucun Serveur Disponible
```
🔍 Test de 4 serveurs...
❌ Serveur local indisponible: http://**************:8069 - timeout
❌ Serveur local indisponible: http://************:8069 - timeout
❌ Serveur local indisponible: http://************:8069 - timeout
❌ Serveur distant indisponible: https://edara.ligne-digitale.com - timeout
❌ Découverte échouée: Aucun serveur Odoo n'est accessible actuellement
```

## 🎯 Avantages du Système

### Pour l'Utilisateur
- **Démarrage automatique** sans configuration manuelle
- **Connexion optimale** (local prioritaire)
- **Feedback visuel** en temps réel
- **Gestion d'erreurs** avec possibilité de réessayer

### Pour l'Administration
- **Configuration centralisée** via serveur_ip.txt
- **Logs détaillés** pour diagnostic
- **Fallbacks automatiques** pour la continuité de service
- **Tests de connectivité** intégrés

### Pour le Développement
- **Code modulaire** et réutilisable
- **APIs bien définies** pour extensions futures
- **Gestion d'erreurs** robuste
- **Tests automatisés** possibles

## 🔍 Debugging et Maintenance

### Points de Contrôle
1. **Fichier serveur_ip.txt** existe et est lisible
2. **Serveurs configurés** sont accessibles
3. **Timeouts** appropriés (5 secondes par défaut)
4. **Logs détaillés** pour diagnostic

### Résolution de Problèmes
- **Splash reste bloqué** → Vérifier serveur_ip.txt
- **Aucun serveur trouvé** → Vérifier connectivité réseau
- **Erreur de parsing** → Vérifier format serveur_ip.txt
- **Timeout trop court** → Ajuster dans ServerDiscovery

## 🚀 Utilisation

### Démarrage Normal
```bash
npm start
```

### Test Manuel de Découverte
```bash
node -e "
const ServerDiscovery = require('./src/main/server-discovery');
const discovery = new ServerDiscovery();
discovery.discoverBestServer(console.log).then(console.log);
"
```

---

**🎯 OBJECTIF ATTEINT** : Démarrage intelligent avec découverte automatique des serveurs et priorisation locale/distante.
