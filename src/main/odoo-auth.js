/**
 * Module d'authentification Odoo pour l'application Edara
 * Gère l'authentification via l'API XML-RPC d'Odoo
 */

const axios = require('axios');
const log = require('electron-log');

class OdooAuth {
  constructor() {
    this.defaultDatabase = 'ligne-digitale';
    this.authTimeout = 10000; // 10 secondes
  }

  /**
   * Authentifier un utilisateur sur Odoo
   * @param {string} username - Nom d'utilisateur ou email
   * @param {string} password - Mot de passe
   * @param {string} serverUrl - URL du serveur Odoo
   * @param {string} database - Nom de la base de données (optionnel)
   * @returns {Promise<Object>} - Résultat de l'authentification
   */
  async authenticate(username, password, serverUrl, database = null) {
    try {
      log.info(`Tentative d'authentification pour ${username} sur ${serverUrl}`);
      
      const dbName = database || this.defaultDatabase;
      
      // Préparer la requête XML-RPC pour l'authentification
      const authData = this.buildAuthRequest(username, password, dbName);
      
      // Effectuer la requête d'authentification
      const response = await axios.post(
        `${serverUrl}/xmlrpc/2/common`,
        authData,
        {
          headers: {
            'Content-Type': 'text/xml',
            'User-Agent': 'Edara-Electron-App/1.0'
          },
          timeout: this.authTimeout
        }
      );
      
      // Parser la réponse XML-RPC
      const authResult = this.parseAuthResponse(response.data);
      
      if (authResult.success) {
        log.info(`Authentification réussie pour ${username} (UID: ${authResult.userId})`);
        
        // Obtenir les informations de session
        const sessionInfo = await this.getSessionInfo(serverUrl, authResult.userId, password, dbName);
        
        return {
          success: true,
          userId: authResult.userId,
          name: authResult.name || username,
          sessionId: sessionInfo.sessionId,
          dbName: dbName,
          serverUrl: serverUrl
        };
      } else {
        log.error(`Échec de l'authentification pour ${username}: ${authResult.error}`);
        return {
          success: false,
          error: authResult.error || 'Identifiants invalides'
        };
      }
      
    } catch (error) {
      log.error(`Erreur lors de l'authentification:`, error);
      
      if (error.code === 'ECONNREFUSED') {
        return {
          success: false,
          error: 'Impossible de se connecter au serveur Odoo'
        };
      } else if (error.code === 'ETIMEDOUT') {
        return {
          success: false,
          error: 'Timeout de connexion au serveur'
        };
      } else {
        return {
          success: false,
          error: error.message || 'Erreur inconnue lors de l\'authentification'
        };
      }
    }
  }

  /**
   * Construire la requête XML-RPC pour l'authentification
   * @param {string} username - Nom d'utilisateur
   * @param {string} password - Mot de passe
   * @param {string} database - Nom de la base de données
   * @returns {string} - Requête XML-RPC
   */
  buildAuthRequest(username, password, database) {
    return `<?xml version="1.0"?>
<methodCall>
  <methodName>authenticate</methodName>
  <params>
    <param><value><string>${database}</string></value></param>
    <param><value><string>${username}</string></value></param>
    <param><value><string>${password}</string></value></param>
    <param><value><struct></struct></value></param>
  </params>
</methodCall>`;
  }

  /**
   * Parser la réponse XML-RPC d'authentification
   * @param {string} xmlResponse - Réponse XML du serveur
   * @returns {Object} - Résultat parsé
   */
  parseAuthResponse(xmlResponse) {
    try {
      // Rechercher l'UID dans la réponse XML
      const uidMatch = xmlResponse.match(/<int>(\d+)<\/int>/);
      
      if (uidMatch && parseInt(uidMatch[1]) > 0) {
        return {
          success: true,
          userId: parseInt(uidMatch[1])
        };
      } else {
        // Rechercher un message d'erreur
        const faultMatch = xmlResponse.match(/<fault>.*?<string>(.*?)<\/string>/s);
        const errorMessage = faultMatch ? faultMatch[1] : 'Authentification échouée';
        
        return {
          success: false,
          error: errorMessage
        };
      }
    } catch (error) {
      log.error('Erreur lors du parsing de la réponse XML:', error);
      return {
        success: false,
        error: 'Erreur lors du traitement de la réponse du serveur'
      };
    }
  }

  /**
   * Obtenir les informations de session après authentification
   * @param {string} serverUrl - URL du serveur
   * @param {number} userId - ID de l'utilisateur
   * @param {string} password - Mot de passe
   * @param {string} database - Nom de la base de données
   * @returns {Promise<Object>} - Informations de session
   */
  async getSessionInfo(serverUrl, userId, password, database) {
    try {
      log.debug('Récupération des informations de session...');
      
      // Effectuer une requête vers l'interface web pour obtenir une session
      const loginResponse = await axios.post(
        `${serverUrl}/web/session/authenticate`,
        {
          jsonrpc: '2.0',
          method: 'call',
          params: {
            db: database,
            login: userId.toString(),
            password: password
          }
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: this.authTimeout
        }
      );
      
      // Extraire l'ID de session des cookies
      const cookies = loginResponse.headers['set-cookie'] || [];
      let sessionId = null;
      
      for (const cookie of cookies) {
        const sessionMatch = cookie.match(/session_id=([^;]+)/);
        if (sessionMatch) {
          sessionId = sessionMatch[1];
          break;
        }
      }
      
      if (!sessionId) {
        // Générer un ID de session temporaire si non trouvé
        sessionId = this.generateSessionId();
        log.warn('Session ID non trouvé dans les cookies, génération d\'un ID temporaire');
      }
      
      log.debug(`Session ID obtenu: ${sessionId.substring(0, 8)}...`);
      
      return {
        sessionId: sessionId
      };
      
    } catch (error) {
      log.warn('Erreur lors de la récupération de la session, génération d\'un ID temporaire:', error.message);
      
      // Fallback: générer un ID de session temporaire
      return {
        sessionId: this.generateSessionId()
      };
    }
  }

  /**
   * Générer un ID de session temporaire
   * @returns {string} - ID de session généré
   */
  generateSessionId() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return `${timestamp}_${random}`;
  }

  /**
   * Vérifier si un utilisateur est toujours authentifié
   * @param {string} serverUrl - URL du serveur
   * @param {string} sessionId - ID de session
   * @returns {Promise<boolean>} - true si la session est valide
   */
  async verifySession(serverUrl, sessionId) {
    try {
      log.debug('Vérification de la validité de la session...');
      
      const response = await axios.post(
        `${serverUrl}/web/session/get_session_info`,
        {
          jsonrpc: '2.0',
          method: 'call',
          params: {}
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Cookie': `session_id=${sessionId}`
          },
          timeout: 5000
        }
      );
      
      const result = response.data.result;
      const isValid = result && result.uid && result.uid > 0;
      
      log.debug(`Session ${isValid ? 'valide' : 'invalide'}`);
      return isValid;
      
    } catch (error) {
      log.debug('Erreur lors de la vérification de session:', error.message);
      return false;
    }
  }

  /**
   * Déconnecter un utilisateur
   * @param {string} serverUrl - URL du serveur
   * @param {string} sessionId - ID de session
   * @returns {Promise<boolean>} - true si la déconnexion a réussi
   */
  async logout(serverUrl, sessionId) {
    try {
      log.info('Déconnexion de l\'utilisateur...');
      
      await axios.post(
        `${serverUrl}/web/session/destroy`,
        {
          jsonrpc: '2.0',
          method: 'call',
          params: {}
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Cookie': `session_id=${sessionId}`
          },
          timeout: 5000
        }
      );
      
      log.info('Déconnexion réussie');
      return true;
      
    } catch (error) {
      log.warn('Erreur lors de la déconnexion:', error.message);
      return false;
    }
  }
}

module.exports = OdooAuth;
