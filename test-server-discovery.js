/**
 * Test simple du module de découverte des serveurs
 */

const ServerDiscovery = require('./src/main/server-discovery');

async function testServerDiscovery() {
  console.log('🧪 === TEST DÉCOUVERTE DES SERVEURS ===');
  
  try {
    const discovery = new ServerDiscovery();
    
    console.log('📋 Chargement de la configuration...');
    await discovery.loadServerConfig();
    
    console.log('🔍 Lancement de la découverte...');
    const result = await discovery.discoverBestServer((progress) => {
      console.log(`📊 Progression: Étape ${progress.step} - ${progress.message}`);
      if (progress.servers) {
        progress.servers.forEach(server => {
          console.log(`   ${server.name}: ${server.statusText}`);
        });
      }
    });
    
    console.log('\n🎯 === RÉSULTAT ===');
    if (result.success) {
      console.log('✅ Découverte réussie !');
      console.log(`🎯 Serveur sélectionné: ${result.server.url} (${result.server.type})`);
      console.log(`⏱️ Temps de réponse: ${result.server.responseTime}ms`);
      console.log(`📊 Total testé: ${result.totalTested}, disponibles: ${result.totalAvailable}`);
    } else {
      console.log('❌ Découverte échouée');
      console.log(`💥 Erreur: ${result.error}`);
    }
    
  } catch (error) {
    console.log('💥 Erreur critique:', error.message);
  }
}

// Exécuter le test
testServerDiscovery();
